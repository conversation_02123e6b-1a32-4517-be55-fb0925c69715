import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
from urllib.parse import quote
import re
from datetime import datetime
import csv
import feedparser

# Headers to avoid blocking
headers = {
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0",
    "Accept-Language": "hi-IN,hi;q=0.9,en-US;q=0.8,en;q=0.7",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "DNT": "1",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
}

def scrape_google_news_hindi(query, num_results=100, max_retries=3):
    """Scrape Google News Hindi for specific query"""
    encoded_query = quote(query)
    url = f"https://www.google.com/search?q={encoded_query}&hl=hi&gl=in&tbm=nws&num={num_results}"

    print(f"  Requesting URL: {url}")

    for attempt in range(max_retries):
        try:
            # Add random delay to avoid rate limiting
            delay = random.uniform(5, 10) + (attempt * 2)  # Longer delays on retries
            print(f"  Waiting {delay:.1f} seconds before request (attempt {attempt + 1}/{max_retries})")
            time.sleep(delay)

            response = requests.get(url, headers=headers, timeout=30)
            print(f"  Response status: {response.status_code}")

            if response.status_code == 429:
                print(f"  Rate limited (429), waiting longer before retry...")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(30, 60))  # Wait 30-60 seconds on rate limit
                    continue
                else:
                    print(f"  Max retries reached, skipping this query")
                    return []

            response.raise_for_status()

            soup = BeautifulSoup(response.content, "html.parser")
            news_results = []

            # Debug: Check what elements we can find
            all_divs = soup.find_all("div")
            print(f"  Total divs found: {len(all_divs)}")

            # Try different selectors
            articles_soabef = soup.select("div.SoaBEf")
            print(f"  Articles with SoaBEf class: {len(articles_soabef)}")

            # Try alternative selectors
            articles_alt1 = soup.select("div[data-ved]")
            print(f"  Articles with data-ved: {len(articles_alt1)}")

            articles_alt2 = soup.select("div.Gx5Zad")
            print(f"  Articles with Gx5Zad class: {len(articles_alt2)}")

            # Save HTML for inspection if no articles found
            if len(articles_soabef) == 0:
                with open(f"debug_{query.replace(' ', '_')}.html", "w", encoding="utf-8") as f:
                    f.write(response.text)
                print(f"  Saved HTML to debug_{query.replace(' ', '_')}.html for inspection")

            for article in soup.select("div.SoaBEf"):
                try:
                    title_element = article.select_one("div.MBeuO")
                    source_element = article.select_one(".NUnG9d span")
                    date_element = article.select_one(".LfVVr")

                    if title_element:
                        news_item = {
                            "headline": title_element.get_text().strip(),
                            "source": source_element.get_text().strip() if source_element else "",
                            "date": date_element.get_text().strip() if date_element else "",
                            "category": query,
                            "scraped_at": datetime.now().isoformat()
                        }
                        news_results.append(news_item)
                        print(f"  Found headline: {news_item['headline'][:50]}...")
                except Exception as e:
                    print(f"  Error parsing article: {e}")
                    continue

            print(f"  Total results found: {len(news_results)}")
            return news_results

        except Exception as e:
            print(f"  Error in request (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                print(f"  Retrying in a moment...")
                time.sleep(random.uniform(10, 20))
            else:
                print(f"  All attempts failed")
                return []

def clean_headline(headline):
    """Clean Hindi headlines"""
    if not headline:
        return ""
    headline = re.sub(r'\s+', ' ', headline).strip()
    headline = re.sub(r'[^\u0900-\u097F\s\w]', '', headline)
    if len(headline.split()) < 3:
        return ""
    return headline

def prepare_10k_dataset():
    """Prepare dataset of 10,000 Hindi news headlines"""

    # Hindi news categories
    queries = [
        "राजनीति समाचार", "खेल समाचार", "बॉलीवूड समाचार", "व्यापार समाचार",
        "तकनीक समाचार", "स्वास्थ्य समाचार", "शिक्षा समाचार", "मौसम समाचार",
        "अपराध समाचार", "अंतर्राष्ट्रीय समाचार", "कोविड समाचार", "चुनाव समाचार",
        "आर्थिक समाचार", "सामाजिक समाचार", "पर्यावरण समाचार", "कृषि समाचार",
        "रेलवे समाचार", "सेना समाचार", "न्यायालय समाचार", "फिल्म समाचार",
        "भारत समाचार", "दिल्ली समाचार", "मुंबई समाचार", "कोलकाता समाचार",
        "चेन्नई समाचार", "बेंगलुरु समाचार", "हैदराबाद समाचार", "पुणे समाचार",
        "गुजरात समाचार", "राजस्थान समाचार", "उत्तर प्रदेश समाचार", "बिहार समाचार",
        "महाराष्ट्र समाचार", "तमिलनाडु समाचार", "कर्नाटक समाचार", "आंध्र प्रदेश समाचार",
        "पंजाब समाचार", "हरियाणा समाचार", "केरल समाचार", "ओडिशा समाचार"
    ]

    all_data = []
    seen_headlines = set()

    print("Starting data collection...")

    for i, query in enumerate(queries):
        print(f"Scraping {i+1}/{len(queries)}: {query}")

        # Scrape once per query with better retry logic
        results = scrape_google_news_hindi(query, num_results=50)  # Reduced from 100

        for item in results:
            cleaned = clean_headline(item['headline'])
            if cleaned and cleaned.lower() not in seen_headlines:
                seen_headlines.add(cleaned.lower())
                item['headline_clean'] = cleaned
                all_data.append(item)

                if len(all_data) >= 1000:
                    break

        if len(all_data) >= 1000:
            break

        # Longer delay between different queries
        time.sleep(random.uniform(15, 30))

        if len(all_data) >= 1000:
            break

        print(f"Collected: {len(all_data)} headlines")

    # Trim to exactly 10,000
    final_data = all_data[:10000]

    # Create DataFrame
    df = pd.DataFrame(final_data)

    # Add row ID
    df['id'] = range(1, len(df) + 1)

    # Reorder columns
    df = df[['id', 'headline', 'headline_clean', 'source', 'date', 'category', 'scraped_at']]

    # Save to CSV
    df.to_csv('hindi_news_10k.csv', index=False, encoding='utf-8')

    print(f"\nDataset prepared successfully!")
    print(f"Total rows: {len(df)}")
    print(f"Unique sources: {df['source'].nunique()}")
    print(f"Categories covered: {df['category'].nunique()}")

    return df

# Execute dataset preparation
if __name__ == "__main__":
    dataset = prepare_10k_dataset()
    print("\nFirst 5 headlines:")
    for i in range(5):
        print(f"{i+1}. {dataset.iloc[i]['headline']}")
